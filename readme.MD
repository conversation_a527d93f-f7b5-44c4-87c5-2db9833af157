# PDF Procesor, RAG a REST API

Tento projekt poskytuje sadu nástrojů pro zpracování PDF dokumentů, jejich uložení do vektorové databáze a následné dotazování pomocí sémantického vyhledávání, RAG (Retrieval-Augmented Generation) a REST API.

Generování embeddings (vektorů) probíhá **lokálně** pomocí knihovny `sentence-transformers`, zatímco pro generování odpovědí v RAG řetězci se využívá **Azure OpenAI**.

## Požadavky

- Python 3.9+
- PostgreSQL databáze (lokální nebo vzdálená) s nainstalovaným rozšířením `pgvector`.
- Internetové připojení (pouze pro první spuštění, kdy se stáhne embedding model).

## Instalace

1.  **Naklonujte nebo stáhněte repozitář.**

2.  **Vytvořte konfigurační soubor `.env`** v kořenovém adresáři projektu. Soubor by měl mít následující strukturu a obsahovat vaše přístupové údaje. Všimněte si, že pro embeddings se nastavuje pouze název modelu, žádný API klíč není potřeba.

    ```dotenv
    # Připojení k PostgreSQL databázi
    DATABASE_URL=postgresql://administrator:alsadmin8@localhost:5432/postgres

    # Název lokálního embedding modelu, který se stáhne a použije
    EMBEDDINGS_MODEL=sentence-transformers/all-MiniLM-L6-v2

    # Konfigurace pro Azure OpenAI
    LLM_ENDPOINT_URL=https://<vase-resource-name>.cognitiveservices.azure.com
    LLM_API_KEY=<vas_klic_pro_azure_openai>
    LLM_API_VERSION=2024-12-01-preview
    LLM_MODEL_NAME=<nazev_vaseho_deploymentu_v_azure>
    ```

3.  **Nainstalujte požadované Python balíčky** pomocí přiloženého souboru `requirements.txt`:

    ```bash
    pip install -r requirements.txt
    ```

## Použití skriptu `pdf_processor.py`

Skript se ovládá z příkazové řádky a nabízí několik příkazů.

### 1. Nahrání a zpracování PDF

Zpracuje zadaný PDF soubor a uloží jeho obsah a vektory do databáze. Při prvním spuštění stáhne potřebný embedding model, což může chvíli trvat.

- **Příkaz:** `ingest`
- **Příklad:**
  ```bash
  python pdf_processor.py ingest "C:\cesta\k\vasemu\dokumentu.pdf"
  ```

### 2. Sémantické vyhledávání

Provede rychlé vyhledávání nejpodobnějších částí textu v databázi na základě vašeho dotazu. (Vhodné pro testování, nevrací odpověď od LLM).

- **Příkaz:** `search`
- **Příklad:**
  ```bash
  python pdf_processor.py search "Co je to návrhový vzor singleton?"
  ```

### 3. Dotaz s kontextem (RAG)

Položí dotaz na Azure OpenAI, který jako kontext použije relevantní informace nalezené ve vektorové databázi.

- **Příkaz:** `ask`
- **Příklad:**
  ```bash
  python pdf_processor.py ask "Vytvoř seznam atributů entity Dodatek modulu Smlouvy systému AFM!"
  ```

### 4. Spuštění REST API

Spustí HTTP server, který zpřístupní funkce pro zpracování PDF a dotazování přes REST API.

- **Příkaz:** `serve`
- **Příklad:**
  ```bash
  python pdf_processor.py serve
  ```
- Server poběží na adrese `http://localhost:80`.
- Interaktivní dokumentace API (Swagger UI) je po spuštění dostupná na `http://localhost:80/docs`.
- Podrobná Markdown dokumentace k API je v souboru `api_documentation.md`.

**Upozornění:** Spuštění serveru na portu 80 může ve vašem systému vyžadovat administrátorská oprávnění.