import os
import requests
import json
from dotenv import load_dotenv

# Načtení proměnných prostředí
load_dotenv()

EMBEDDINGS_URL = os.getenv("EMBEDDINGS_URL")
EMBEDDINGS_API_KEY = os.getenv("EMBEDDINGS_API_KEY")

def test_api_formats():
    """Test různých formátů API pro embeddings"""
    
    if not all([EMBEDDINGS_URL, EMBEDDINGS_API_KEY]):
        print("❌ EMBEDDINGS_URL nebo EMBEDDINGS_API_KEY není nastavena!")
        return
    
    test_text = "Hello world test"
    
    headers = {
        "Authorization": f"Bearer {EMBEDDINGS_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # Různé formáty API, které můžeme zkusit - všechny s Seznam modelem
    seznam_model = "Seznam/dist-mpnet-czeng-cs-en"
    api_formats = [
        {
            "name": "OpenAI Compatible",
            "url": f"{EMBEDDINGS_URL.rstrip('/')}/embeddings",
            "payload": {
                "input": test_text,
                "model": seznam_model
            }
        }
    ]
    
    print(f"🧪 Testování různých formátů API pro: {EMBEDDINGS_URL}")
    print("=" * 60)
    
    for format_info in api_formats:
        print(f"\n📝 Zkouším: {format_info['name']}")
        print(f"   URL: {format_info['url']}")
        print(f"   Payload: {json.dumps(format_info['payload'], indent=2)}")
        
        try:
            response = requests.post(
                format_info['url'],
                headers=headers,
                json=format_info['payload'],
                timeout=30
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    
                    # Zkus najít embedding v odpovědi
                    embedding = None
                    if "data" in data and isinstance(data["data"], list) and len(data["data"]) > 0:
                        if "embedding" in data["data"][0]:
                            embedding = data["data"][0]["embedding"]
                    elif "embeddings" in data:
                        if isinstance(data["embeddings"], list) and len(data["embeddings"]) > 0:
                            embedding = data["embeddings"][0]
                        else:
                            embedding = data["embeddings"]
                    elif "embedding" in data:
                        embedding = data["embedding"]
                    elif "vector" in data:
                        embedding = data["vector"]
                    elif isinstance(data, list):
                        embedding = data
                    
                    if embedding and isinstance(embedding, list) and len(embedding) > 0:
                        print(f"   ✅ ÚSPĚCH! Rozměr: {len(embedding)}")
                        print(f"   📊 Ukázka: [{embedding[0]:.4f}, {embedding[1]:.4f}, ...]")
                        
                        # Uložení funkčního formátu
                        print(f"\n🎉 Tento formát funguje! Použij:")
                        print(f"   URL: {format_info['url']}")
                        print(f"   Model: {format_info['payload'].get('model', 'default')}")
                        break
                    else:
                        print(f"   ⚠️  Odpověď neobsahuje platný embedding")
                        print(f"   📄 Odpověď: {json.dumps(data, indent=2)[:200]}...")
                        
                except json.JSONDecodeError:
                    print(f"   ❌ Neplatný JSON v odpovědi")
                    print(f"   📄 Raw odpověď: {response.text[:200]}...")
            else:
                print(f"   ❌ HTTP chyba: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   📄 Chyba: {json.dumps(error_data, indent=2)[:200]}...")
                except:
                    print(f"   📄 Raw chyba: {response.text[:200]}...")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request chyba: {e}")
    
    print(f"\n💡 Pokud žádný formát nefungoval, zkontroluj:")
    print(f"   • API dokumentaci tvého poskytovatele")
    print(f"   • Správnost API klíče")
    print(f"   • Potřeba jiných headers (např. API-Key místo Authorization)")
    print(f"   • Jestli API vyžaduje jiný content-type")

def test_custom_headers():
    """Test s různými header formáty"""
    
    test_text = "Hello world"
    url = f"{EMBEDDINGS_URL.rstrip('/')}/embeddings"
    
    header_formats = [
        {"Authorization": f"Bearer {EMBEDDINGS_API_KEY}"},
        {"API-Key": EMBEDDINGS_API_KEY},
        {"X-API-Key": EMBEDDINGS_API_KEY},
        {"apikey": EMBEDDINGS_API_KEY},
        {"token": EMBEDDINGS_API_KEY},
    ]
    
    payload = {"input": test_text, "model": "*Seznam/dist-mpnet-czeng-cs-en*"}
    
    print(f"\n🔑 Testování různých header formátů:")
    print("-" * 40)
    
    for headers in header_formats:
        headers["Content-Type"] = "application/json"
        print(f"\n📝 Zkouším headers: {list(headers.keys())}")
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Tento header formát funguje!")
                return headers
            else:
                print(f"   ❌ {response.status_code}: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Chyba: {e}")
    
    return None

if __name__ == "__main__":
    print("🔧 Embedding API Tester")
    print("=" * 30)
    
    # Test základních formátů
    test_api_formats()
    
    # Test různých headers pokud základní formáty nefungují
    test_custom_headers()