# REST API Dokumentace pro Vector DB

Tato dokumentace popisuje, jak používat REST API pro nahrávání PDF souborů a následné dotazování nad jejich obsahem pomocí sémantického vyhledávání.

**Base URL:** `http://localhost:80`

---

## 1. Zpracování PDF souboru

Endpoint pro nahrání nového PDF souboru, jeho zpracování a uložení do vektorové databáze.

- **Endpoint:** `/process-pdf/`
- **Metoda:** `POST`
- **Popis:** Přijímá PDF soubor zakódovaný jako Base64 řetězec a spustí proces jeho chunkování, vytvoření embeddings a uložení.

### Struktura JSON Payload

Požadavek musí obsahovat JSON objekt s následujícími k<PERSON>:

```json
{
  "file_name": "nazev_vaseho_souboru.pdf",
  "pdf_base64": "<dlouhý_řetězec_s_obsahem_pdf_v_base64>"
}
```

- `file_name` (string, povinné): Název souboru, slouží pro informační účely v odpovědi.
- `pdf_base64` (string, povinné): Obsah PDF souboru zakódovaný ve formátu Base64.

### Příklad `curl` volání

```bash
# Nejprve si soubor zakódujte do Base64
# Linux/macOS:
# base64 my_document.pdf > my_document.b64
# Windows (PowerShell):
# [Convert]::ToBase64String([IO.File]::ReadAllBytes("my_document.pdf")) | Out-File -FilePath my_document.b64 -Encoding ascii

# Poté vložte obsah souboru my_document.b64 do payloadu
curl -X POST http://localhost:80/process-pdf/ \
-H "Content-Type: application/json" \
-d \
'{
  "file_name": "my_document.pdf",
  "pdf_base64": "JVBERi0xLjcNCiW...<zbytek base64>...JUIwZg=="
}'
```

### Odpovědi

- **200 OK (Úspěch):**
  ```json
  {
    "status": "success",
    "message": "Soubor 'my_document.pdf' byl úspěšně zpracován."
  }
  ```

- **400 Bad Request (Chyba):** Pokud Base64 řetězec není platný.
  ```json
  {
    "detail": "Neplatný Base64 kód: ..."
  }
  ```

---

## 2. Dotazování databáze

Endpoint pro sémantické vyhledávání ve vektorové databázi.

- **Endpoint:** `/query/`
- **Metoda:** `POST`
- **Popis:** Provede sémantické vyhledávání na základě textového dotazu a vrátí 3 nejpodobnější textové chunky z databáze.

### Struktura JSON Payload

Požadavek musí obsahovat JSON objekt s následujícím klíčem:

```json
{
  "query": "Váš textový dotaz sem"
}
```

- `query` (string, povinné): Text, na základě kterého se má provést sémantické vyhledávání.

### Příklad `curl` volání

```bash
curl -X POST http://localhost:80/query/ \
-H "Content-Type: application/json" \
-d \
'{
  "query": "Jaké jsou hlavní výhody produktu?"
}'
```

### Odpovědi

- **200 OK (Úspěch):**
  ```json
  {
    "status": "success",
    "results": [
      {
        "score": 0.851234567,
        "content": "Text nalezeného chunku, který nejvíce odpovídá dotazu...",
        "metadata": {
          "source": "/tmp/tmpabcdef.pdf",
          "page": 5
        }
      },
      {
        "score": 0.829876543,
        "content": "Další relevantní textový chunk...",
        "metadata": {
          "source": "/tmp/tmpabcdef.pdf",
          "page": 12
        }
      }
    ]
  }
  ```

- **400 Bad Request (Chyba):** Pokud je dotaz prázdný.
  ```json
  {
    "detail": "Dotaz nemůže být prázdný."
  }
  ```
- **500 Internal Server Error (Chyba):** V případě obecné chyby při komunikaci s databází.
