import os
import argparse
import base64
import tempfile
import uvicorn
from typing import List

from dotenv import load_dotenv
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_postgres import PGVector
from langchain_community.vectorstores.pgvector import DistanceStrategy
from langchain_core.embeddings import Embeddings
from langchain_openai import AzureChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from sentence_transformers import SentenceTransformer

# --- Načtení konfiguračních proměnných ---
load_dotenv()

# Sestavení správného DATABASE_URL
DATABASE_HOST = os.getenv("DATABASE_HOST", "localhost")
DATABASE_PORT = os.getenv("DATABASE_PORT", "5432")
DATABASE_NAME = os.getenv("DATABASE_NAME", "postgres")
DATABASE_USER = os.getenv("DATABASE_USER", "administrator")
DATABASE_PASSWORD = os.getenv("DATABASE_PASSWORD")

# Sestavení kompletního connection stringu
DATABASE_URL = f"postgresql://{DATABASE_USER}:{DATABASE_PASSWORD}@{DATABASE_HOST}:{DATABASE_PORT}/{DATABASE_NAME}"

COLLECTION_NAME = "pdf_documents_collection"

# Embeddings model - s fallback na fungující model
EMBEDDINGS_MODEL = os.getenv("EMBEDDINGS_MODEL", 'Seznam/simcse-small-e-czech')

# LLM pro Azure
LLM_ENDPOINT_URL = os.getenv("LLM_ENDPOINT_URL")
LLM_API_KEY = os.getenv("LLM_API_KEY")
LLM_API_VERSION = os.getenv("LLM_API_VERSION")
LLM_MODEL_NAME = os.getenv("LLM_MODEL_NAME")

# --- Třída pro lokální Sentence Transformer embeddings s lepším error handlingem ---
class CustomSentenceTransformerEmbeddings(Embeddings):
    def __init__(self, model_name: str):
        print(f"Pokouším se načíst model '{model_name}'...")
        try:
            self.model = SentenceTransformer(model_name)
            print(f"Model '{model_name}' úspěšně načten.")
            self.model_name = model_name
        except Exception as e:
            print(f"CHYBA při načítání modelu '{model_name}': {e}")
            fallback_model = 'Seznam/simcse-small-e-czech'
            print(f"Používám fallback model: '{fallback_model}'")
            self.model = SentenceTransformer(fallback_model)
            self.model_name = fallback_model
        
        # Test embeddings
        test_embedding = self.model.encode("test")
        print(f"Rozměr embeddings: {len(test_embedding)}")

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        print(f"Vytvářím embeddings pro {len(texts)} dokumentů...")
        embeddings = self.model.encode(texts, convert_to_tensor=False)
        return [e.tolist() for e in embeddings]

    def embed_query(self, text: str) -> List[float]:
        print(f"Vytvářím embedding pro dotaz: '{text[:50]}...'")
        embedding = self.model.encode(text, convert_to_tensor=False)
        return embedding.tolist()

# --- Globální instance embedding modelu ---
embedding_function = None

def get_embedding_function():
    global embedding_function
    if embedding_function is None:
        embedding_function = CustomSentenceTransformerEmbeddings(EMBEDDINGS_MODEL)
    return embedding_function

# --- Funkce pro zpracování a uložení PDF ---
def ingest_pdf(file_path: str, chunk_size: int = 1000, chunk_overlap: int = 100):
    if not os.path.exists(file_path):
        print(f"Chyba: Soubor '{file_path}' neexistuje.")
        return False
    
    print(f"Zpracovávám soubor: {file_path}")
    print(f"Používám DATABASE_URL: {DATABASE_URL}")
    
    try:
        loader = PyPDFLoader(file_path)
        documents = loader.load()
        print(f"Načteno {len(documents)} stránek z PDF.")

        if not documents:
            print("CHYBA: Nepodařilo se načíst žádný obsah z PDF!")
            return False

        # --- DEBUGGING KROK ---
        print("\n--- DEBUG: Surový extrahovaný text z prvních 3 stran ---")
        debug_file_path = "debug_extracted_text.txt"
        try:
            with open(debug_file_path, "w", encoding="utf-8") as f:
                for i, doc in enumerate(documents[:3]):
                    page_header = f"--- Strana {i+1} ---\n"
                    separator = "-" * 50 + "\n"
                    
                    print(page_header)
                    print(f"Délka textu: {len(doc.page_content)} znaků")
                    print(f"První 200 znaků: {doc.page_content[:200]}")
                    print(separator)
                    
                    f.write(page_header)
                    f.write(f"Délka textu: {len(doc.page_content)} znaků\n")
                    f.write(doc.page_content + "\n")
                    f.write(separator)
            print(f"--- Debug text uložen do: {debug_file_path} ---")
        except Exception as e:
            print(f"CHYBA při zápisu debug souboru: {e}")
        # --- KONEC DEBUGGING KROKU ---

        # Text splitting s debug informacemi
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, 
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        docs = text_splitter.split_documents(documents)
        print(f"Dokument rozdělen na {len(docs)} chunků (velikost: {chunk_size}, překryv: {chunk_overlap}).")
        
        # Debug info o chuncích
        if docs:
            print(f"První chunk délka: {len(docs[0].page_content)} znaků")
            print(f"První chunk náhled: {docs[0].page_content[:100]}...")
        
        embeddings = get_embedding_function()
        print("Vytvářím a ukládám embeddings do databáze...")
        
        # Test vytvoření embeddings
        test_embedding = embeddings.embed_query("test")
        print(f"Test embedding úspěšný, rozměr: {len(test_embedding)}")
        
        vectorstore = PGVector.from_documents(
            documents=docs,
            embedding=embeddings,
            collection_name=COLLECTION_NAME,
            connection=DATABASE_URL,
            distance_strategy=DistanceStrategy.COSINE,
        )
        
        print("Hotovo! Dokument byl úspěšně zpracován a uložen.")
        
        # Test dotazu ihned po uložení
        print("\n--- TEST DOTAZU PO ULOŽENÍ ---")
        test_results = vectorstore.similarity_search("číslo", k=2)
        print(f"Test nalezl {len(test_results)} výsledků")
        for i, result in enumerate(test_results):
            print(f"Výsledek {i+1}: {result.page_content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"CHYBA při zpracování PDF: {e}")
        return False

# --- Funkce pro sémantické vyhledávání s debug informacemi ---
def search_semantic(query: str):
    print(f"=== SÉMANTICKÉ VYHLEDÁVÁNÍ ===")
    print(f"Dotaz: '{query}'")
    print(f"DATABASE_URL: {DATABASE_URL}")
    
    try:
        embeddings = get_embedding_function()
        print(f"Embedding model: {embeddings.model_name}")
        
        # Test embedding dotazu
        query_embedding = embeddings.embed_query(query)
        print(f"Query embedding rozměr: {len(query_embedding)}")
        
        db = PGVector(
            collection_name=COLLECTION_NAME,
            connection=DATABASE_URL,
            embeddings=embeddings,
            distance_strategy=DistanceStrategy.COSINE,
        )
        
        print(f"Hledám podobné dokumenty v kolekci '{COLLECTION_NAME}'...")
        docs_with_scores = db.similarity_search_with_score(query, k=5)
        
        if not docs_with_scores:
            print("❌ Nebyly nalezeny žádné relevantní dokumenty.")
            print("Možné příčiny:")
            print("- Databáze je prázdná")
            print("- Nekompatibilní embedding modely")
            print("- Špatné připojení k databázi")
            return
            
        print(f"\n✅ Nalezeno {len(docs_with_scores)} výsledků:")
        print("=" * 60)
        
        for i, (doc, score) in enumerate(docs_with_scores, 1):
            print(f"Výsledek {i}:")
            print(f"Podobnost (cosine distance): {score:.4f}")
            print(f"Zdroj: {doc.metadata.get('source', 'N/A')}")
            print(f"Strana: {doc.metadata.get('page', 'N/A')}")
            print(f"Délka obsahu: {len(doc.page_content)} znaků")
            print(f"Obsah (prvních 300 znaků):")
            print(f"{doc.page_content[:300]}...")
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ CHYBA při vyhledávání: {e}")
        import traceback
        traceback.print_exc()

# --- Funkce pro RAG ---
def answer_with_rag(query: str):
    print("=== RAG ODPOVĚĎ ===")
    try:
        embeddings = get_embedding_function()
        vectorstore = PGVector(
            collection_name=COLLECTION_NAME,
            connection=DATABASE_URL,
            embeddings=embeddings,
            distance_strategy=DistanceStrategy.COSINE,
        )
        
        # Test retrieval
        print("Testujem retrieval...")
        retriever = vectorstore.as_retriever(search_kwargs={"k": 3})
        retrieved_docs = retriever.invoke(query)
        print(f"Nalezeno {len(retrieved_docs)} dokumentů pro kontext")
        
        if not retrieved_docs:
            print("❌ Žádné dokumenty nebyly nalezeny pro vytvoření kontextu!")
            return
        
        # Inicializace Azure OpenAI
        llm = AzureChatOpenAI(
            azure_endpoint=LLM_ENDPOINT_URL,
            api_key=LLM_API_KEY,
            api_version=LLM_API_VERSION,
            azure_deployment=LLM_MODEL_NAME,
            temperature=0
        )

        prompt = ChatPromptTemplate.from_template("""
Odpověz na otázku na základě poskytnutého kontextu. Pokud odpověď není v kontextu, řekni to.

Kontext: {context}

Otázka: {question}

Odpověď:
""")
        
        def format_docs(docs):
            formatted = "\n\n".join(f"[Dokument {i+1}]:\n{doc.page_content}" for i, doc in enumerate(docs))
            print(f"Kontext pro LLM (délka: {len(formatted)} znaků)")
            return formatted
            
        rag_chain = (
            {"context": retriever | format_docs, "question": RunnablePassthrough()}
            | prompt
            | llm
            | StrOutputParser()
        )
        
        print(f"\nPokládám dotaz: '{query}'")
        print("\n--- Odpověď z Azure OpenAI ---")
        response = ""
        for chunk in rag_chain.stream(query):
            print(chunk, end="", flush=True)
            response += chunk
        print("\n" + "=" * 40)
        
    except Exception as e:
        print(f"❌ CHYBA při RAG: {e}")
        import traceback
        traceback.print_exc()

# --- API ČÁST ---
app = FastAPI(title="Vector DB API")

class PDFProcessRequest(BaseModel):
    file_name: str
    pdf_base64: str

class QueryRequest(BaseModel):
    query: str

def query_vector_db_for_api(query: str) -> List[dict]:
    embeddings = get_embedding_function()
    db = PGVector(
        collection_name=COLLECTION_NAME, 
        connection=DATABASE_URL, 
        embeddings=embeddings,
        distance_strategy=DistanceStrategy.COSINE,
    )
    docs_with_scores = db.similarity_search_with_score(query, k=3)
    results = []
    for doc, score in docs_with_scores:
        results.append({
            "score": float(score), 
            "content": doc.page_content, 
            "metadata": doc.metadata
        })
    return results

@app.post("/process-pdf/", summary="Zpracuje PDF soubor")
async def process_pdf_endpoint(request: PDFProcessRequest):
    try:
        pdf_data = base64.b64decode(request.pdf_base64)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Neplatný Base64 kód: {e}")
    
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
        tmp_file.write(pdf_data)
        tmp_file_path = tmp_file.name
    
    try:
        success = ingest_pdf(tmp_file_path)
        if success:
            return {"status": "success", "message": f"Soubor '{request.file_name}' byl úspěšně zpracován."}
        else:
            raise HTTPException(status_code=500, detail="Chyba při zpracování PDF")
    finally:
        if os.path.exists(tmp_file_path):
            os.remove(tmp_file_path)

@app.post("/query/", summary="Dotaz do vektorové DB")
async def query_endpoint(request: QueryRequest):
    if not request.query:
        raise HTTPException(status_code=400, detail="Dotaz nemůže být prázdný.")
    try:
        results = query_vector_db_for_api(request.query)
        return {"status": "success", "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při dotazování databáze: {e}")

# --- Hlavní část skriptu ---
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Nástroj pro PDF, RAG a API.")
    subparsers = parser.add_subparsers(dest="command", required=True)

    ingest_parser = subparsers.add_parser("ingest", help="Načte a zpracuje PDF soubor do databáze.")
    ingest_parser.add_argument("filepath", type=str, help="Cesta k PDF souboru.")
    ingest_parser.add_argument("--chunk-size", type=int, default=1000, help="Velikost chunku.")
    ingest_parser.add_argument("--chunk-overlap", type=int, default=200, help="Překryv chunků.")

    search_parser = subparsers.add_parser("search", help="Provede sémantické vyhledávání.")
    search_parser.add_argument("query", type=str, help="Textový dotaz pro vyhledávání.")

    ask_parser = subparsers.add_parser("ask", help="Položí dotaz LLM s kontextem z databáze.")
    ask_parser.add_argument("query", type=str, help="Otázka pro LLM.")

    serve_parser = subparsers.add_parser("serve", help="Spustí HTTP REST API server.")

    # Debug parser pro diagnostiku
    debug_parser = subparsers.add_parser("debug", help="Diagnostické informace.")

    args = parser.parse_args()

    if args.command == "debug":
        print("=== DIAGNOSTICKÉ INFORMACE ===")
        print(f"DATABASE_URL: {DATABASE_URL}")
        print(f"EMBEDDINGS_MODEL: {EMBEDDINGS_MODEL}")
        print(f"LLM_MODEL_NAME: {LLM_MODEL_NAME}")
        
        # Test embedding funkce
        embeddings = get_embedding_function()
        test_embedding = embeddings.embed_query("test query")
        print(f"Test embedding úspěšný, rozměr: {len(test_embedding)}")
        
        # Test připojení k databázi
        try:
            db = PGVector(
                collection_name=COLLECTION_NAME,
                connection=DATABASE_URL,
                embeddings=embeddings,
                distance_strategy=DistanceStrategy.COSINE,
            )
            # Pokus o test dotaz
            test_results = db.similarity_search("test", k=1)
            print(f"Test databáze úspěšný, nalezeno {len(test_results)} výsledků")
        except Exception as e:
            print(f"❌ CHYBA připojení k databázi: {e}")
    
    elif args.command == "ingest":
        ingest_pdf(args.filepath, chunk_size=args.chunk_size, chunk_overlap=args.chunk_overlap)
    elif args.command == "search":
        search_semantic(args.query)
    elif args.command == "ask":
        answer_with_rag(args.query)
    elif args.command == "serve":
        print("Spouštím FastAPI server na http://0.0.0.0:80")
        uvicorn.run(app, host="0.0.0.0", port=80)